package com.marketing.management.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分支机构分类评级实体
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("branch_rating")
public class BranchRating implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 生效开始日期
     */
    private String effectiveStartDate;

    /**
     * 生效结束日期
     */
    private String effectiveEndDate;

    /**
     * 分支机构代码
     */
    private String branchCode;

    /**
     * 分支机构名称
     */
    private String branchName;

    /**
     * 分类评定等级
     */
    private String ratingLevel;

    /**
     * 是否为最新记录
     */
    private Boolean isLatest;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private Integer deleted;
}
