<template>
  <div class="report-title">
    <h2>分支机构考核得分</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计日期" required>
              <el-date-picker
                v-model="queryForm.dataDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="data_date" label="日期" fixed="left" />
        <el-table-column prop="brh_cd" label="分支机构代码" sortable/>
        <el-table-column prop="brh_nam" label="分支机构名称" />
        <el-table-column prop="clas_rat_grad" label="分类评定等级" />
        <el-table-column prop="p_scor" label="利润得分" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.p_scor) }}
          </template>
        </el-table-column>
        <el-table-column prop="incm_scor" label="收入得分" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.incm_scor) }}
          </template>
        </el-table-column>
        <el-table-column prop="avg_equi_scor" label="日均权益得分" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.avg_equi_scor) }}
          </template>
        </el-table-column>
        <el-table-column prop="ib_scor" label="IB融合得分" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.ib_scor) }}
          </template>
        </el-table-column>
        <el-table-column prop="team_scor" label="团队建设得分" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.team_scor) }}
          </template>
        </el-table-column>
        <el-table-column prop="espe_scor" label="特色发展得分" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.espe_scor) }}
          </template>
        </el-table-column>
        <el-table-column prop="oth_aasp_scor" label="其他加减分" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.oth_aasp_scor) }}
          </template>
        </el-table-column>
        <el-table-column prop="perf_exam_last_scor" label="绩效考核最终得分" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.perf_exam_last_scor) }}
          </template>
        </el-table-column>
        <el-table-column prop="exam_scor_rank" label="考核得分总排名" align="right"  />
        <el-table-column prop="clas_scor_rank" label="分类得分排名" align="right"  />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

const router = useRouter()
const route = useRoute()

// 获取URL参数
const oacode = ref(route.query.oacode || 'system')
const srcsys = ref(route.query.srcsys || '1')

// 获取默认日期（上个月）
const getDefaultDate = () => {
  const now = new Date()
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  return `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`
}

// 查询表单
const queryForm = reactive({
  dataDate: getDefaultDate(),
  branchCode: ''
})

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacode: params.get('oacode') || oacode.value,
    roleid: params.get('roleid') || '001',
    srcsys: params.get('srcsys') || srcsys.value,
    goBack: params.get('goback') || false,
  }
}
const urlParams = getUrlParams()

// 格式化数字
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 4
  })
}

// 初始化数据
onMounted(() => {
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询方法
const handleQuery = async () => {
  // 验证必填字段
  if (!queryForm.dataDate) {
    ElMessage.warning('请选择统计日期')
    return
  }

  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构建函数参数
  const functionParams = {
    oacode: urlParams.oacode,
    srcsys: urlParams.srcsys,
    roleid: urlParams.roleid,
    goBack: urlParams.goBack,
    p_data_date: queryForm.dataDate,
    p_brh_cd: queryForm.branchCode || null
  }

  const config = {
    headers: {
      'Accept': 'application/json',
      'Content-Profile': 'mkt_base'
    }
  }

  http.callFunction('p_brh_exam_scor_s', functionParams, config)
  .then(response => {
    let rawData = response.data || []

    // 对数据进行排序（按考核得分总排名升序）
    rawData = rawData.sort((a, b) => {
      const rankA = a.exam_scor_rank || 999999
      const rankB = b.exam_scor_rank || 999999
      return rankA - rankB
    })

    // 进行分页处理
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    const paginatedData = rawData.slice(startIndex, endIndex)

    tableData.value = paginatedData
    totalCount.value = rawData.length
  })
  .catch(error => {
    console.error('API请求失败:', error)
    ElMessage.error('获取数据失败')
  })
  .finally(() => {
    lastSuccessfulForm = JSON.stringify(queryForm);
    loading.value = false
  })
}

// 导出方法
const handleExport = async () => {
  // 验证必填字段
  if (!queryForm.dataDate) {
    ElMessage.warning('请选择统计日期')
    return
  }

  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建函数参数
    const functionParams = {
      oacode: urlParams.oacode,
      srcsys: urlParams.srcsys,
      roleid: urlParams.roleid,
      goBack: urlParams.goBack,
      p_data_date: queryForm.dataDate,
      p_brh_cd: queryForm.branchCode || null
    }

    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.callFunction('p_brh_exam_scor_s', functionParams, config)
    const aggregatedData = response.data || []

    if (aggregatedData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '日期', '分支机构代码', '分支机构名称', '分类评定等级', '利润得分', '收入得分',
        '日均权益得分', 'IB融合得分', '团队建设得分', '特色发展得分', '其他加减分',
        '绩效考核最终得分', '考核得分总排名', '分类得分排名'
      ],
      // 数据行
      ...aggregatedData.map(item => [
        item.data_date || '',
        item.brh_cd || '',
        item.brh_nam || '',
        item.clas_rat_grad || '',
        formatNumber(item.p_scor),
        formatNumber(item.incm_scor),
        formatNumber(item.avg_equi_scor),
        formatNumber(item.ib_scor),
        formatNumber(item.team_scor),
        formatNumber(item.espe_scor),
        formatNumber(item.oth_aasp_scor),
        formatNumber(item.perf_exam_last_scor),
        item.exam_scor_rank || '',
        item.clas_scor_rank || ''
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 日期
      { wch: 15 }, // 分支机构代码
      { wch: 25 }, // 分支机构名称
      { wch: 15 }, // 分类评定等级
      { wch: 12 }, // 利润得分
      { wch: 12 }, // 收入得分
      { wch: 15 }, // 日均权益得分
      { wch: 15 }, // IB融合得分
      { wch: 15 }, // 团队建设得分
      { wch: 15 }, // 特色发展得分
      { wch: 15 }, // 其他加减分
      { wch: 18 }, // 绩效考核最终得分
      { wch: 18 }, // 考核得分总排名
      { wch: 15 }  // 分类得分排名
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '分支机构考核得分')

    // 生成文件名
    const now = new Date()
    const fileName = `分支机构考核得分_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${aggregatedData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc';
    queryForm.order = `${prop}.${sortOrder}`;
  } else {
    queryForm.order = '';
  }
  handleQuery();
}
</script>

<style lang="scss" scoped>

/* 状态标签样式 */
</style>
