<template>
  <div>
    <el-form-item label="分支机构">
      <el-select
        v-model="selectedBranch"
        placeholder="请输入分支机构代码或名称进行搜索"
        filterable
        remote
        multiple
        reserve-keyword
        :remote-method="remoteSearchBranch"
        clearable
      >
        <el-option
          v-for="item in branchOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span> {{ item.label }}</span>
        </el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import http from '~/http/http.js'

// 分支机构选项
const branchOptions = ref([])

// 定义 emits
const emits = defineEmits(['branch-selected'])

// 选中的分支机构
const selectedBranch = ref([])

// 获取分支机构选项
const loadBranchOptions = async () => {
  try {
    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/v_find_dim_org', {}, config)
    const data = response.data || []

    branchOptions.value = data.map(item => ({
      value: item.brh_cd,
      label: item.brh_nam
    })).sort((a, b) => a.value.localeCompare(b.value))
  } catch (error) {
    console.error('获取分支机构选项失败:', error)
    branchOptions.value = []
  }
}

// 远程搜索分支机构
const remoteSearchBranch = async (query) => {
  if (query) {
    try {
      const config = {
        params: {
          or: `(brh_cd.ilike.*${query}*,brh_nam.ilike.*${query}*)`
        },
        headers: {
          'Accept': 'application/json',
          'Content-Profile': 'mkt_base'
        }
      }

      const response = await http.get('/v_find_dim_org', {}, config)
      const data = response.data || []

      branchOptions.value = data.map(item => ({
        value: item.brh_cd,
        label: item.brh_nam
      })).sort((a, b) => a.value.localeCompare(b.value))
    } catch (error) {
      console.error('搜索分支机构失败:', error)
    }
  } else {
    loadBranchOptions()
  }
}

// 初始化加载分支机构
loadBranchOptions()

// 监听选中值变化
watch(selectedBranch, (newVal) => {
  // 空值传递空字符串
  if (!newVal || newVal.length === 0) {
    emits('branch-selected', '')
  } 
  // 单选传递单个值
  else if (newVal.length === 1) {
    emits('branch-selected', newVal[0])
  }
  // 多选传递逗号分隔的值
  else {
    emits('branch-selected', newVal.join(','))
  }
})
</script>

<style scoped>
</style>