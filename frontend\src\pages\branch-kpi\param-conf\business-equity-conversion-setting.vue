<template>
  <div class="report-title">
    <h2>创新业务权益折算系数设置</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="业务名称">
              <el-select
                v-model="queryForm.businessNames"
                multiple
                clearable
                placeholder="请选择业务名称"
              >
                <el-option
                  v-for="item in businessOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="primary" @click="handleAdd">新增</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="busi_name" label="业务名称" />
        <el-table-column prop="busi_type_name" label="业务类型" />
        <el-table-column prop="cnvr_subj_name" label="折算标的" />
        <el-table-column prop="equi_cnvr_coef" label="权益折算系数" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.equi_cnvr_coef) }}
          </template>
        </el-table-column>
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="info" size="small" @click="handleHistory(scope.row)">历史记录</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="totalCount"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
            <el-form-item label="生效开始日期" prop="tect_strt_date">
              <el-tooltip
                :disabled="!isEdit"
                content="修改模式下，开始日期不能早于原值"
                placement="top"
              >
                <el-date-picker
                  v-model="formData.tect_strt_date"
                  type="month"
                  placeholder="选择月份"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  :disabled-date="disabledStartDate"
                  style="width: 100%"
                />
              </el-tooltip>
            </el-form-item>
        <el-form-item label="生效结束日期" prop="tect_end_date">
          <el-date-picker
            v-model="formData.tect_end_date"
            type="month"
            placeholder="选择结束月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="业务名称" prop="busi_name_cd">
          <el-select
            v-model="formData.busi_name_cd"
            placeholder="请选择业务名称"
            :disabled="isEdit"
          >
            <el-option
              v-for="item in businessOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="业务类型" prop="busi_type">
          <el-select
            v-model="formData.busi_type"
            placeholder="请选择业务类型"
            :disabled="isEdit"
          >
            <el-option
              v-for="item in businessTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="折算标的" prop="cnvr_subj">
          <el-select
            v-model="formData.cnvr_subj"
            placeholder="请选择折算标的"
          >
            <el-option
              v-for="item in cnvrSubjOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="权益折算系数" prop="equi_cnvr_coef">
          <el-input-number
            v-model="formData.equi_cnvr_coef"
            :precision="4"
            :step="0.0001"
            :min="0"
            placeholder="请输入权益折算系数"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog
      title="查看历史记录"
      v-model="historyDialogVisible"
      width="1200px"
    >
      <el-table
        :data="historyData"
        border
        stripe
        table-layout="auto"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="busi_name" label="业务名称" />
        <el-table-column prop="busi_type_name" label="业务类型" />
        <el-table-column prop="cnvr_subj_name" label="折算标的" />
        <el-table-column prop="equi_cnvr_coef" label="权益折算系数" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.equi_cnvr_coef) }}
          </template>
        </el-table-column>
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 查询表单
const queryForm = reactive({
  businessNames: []
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 业务名称选项
const businessOptions = ref([])
// 业务类型选项
const businessTypeOptions = ref([])
// 新增折算标的选择项
const cnvrSubjOptions = ref([])

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)

// 历史记录对话框
const historyDialogVisible = ref(false)
const historyData = ref([])

// 表单
const formRef = ref()
const formData = reactive({
  tect_strt_date: '',
  tect_end_date: '',
  busi_name_cd: '',
  busi_type: '',
  cnvr_subj: '',  // 修改：保持原字段名，但改为下拉选择
  equi_cnvr_coef: null,
  uuid: ''
})

// 表单验证规则
const formRules = {
  tect_strt_date: [
    { required: true, message: '请选择生效开始日期', trigger: 'change' }
  ],
  tect_end_date: [
    { required: true, message: '请选择生效结束日期', trigger: 'change' }
  ],
  busi_name_cd: [
    { required: true, message: '请选择业务名称', trigger: 'change' }
  ],
  busi_type: [
    { required: true, message: '请输入业务类型', trigger: 'blur' }
  ],
  cnvr_subj: [
    { required: true, message: '请输入折算标的', trigger: 'blur' }
  ],
  equi_cnvr_coef: [
    { required: true, message: '请输入权益折算系数', trigger: 'blur' }
  ]
}

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacde: params.get('oacode') || 'current_user',
    roleid: params.get('roleid') || '001'
  }
}

const urlParams = getUrlParams()

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化数字
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 4,
    maximumFractionDigits: 4
  })
}

// 获取业务名称选项
const loadBusinessOptions = async () => {
  try {
    const config = {
      params: {
        dict_code: 'eq.zxkh0001',
        deleted: `eq.0`
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/dictionary_cfg', {}, config)
    const data = response.data || []

    businessOptions.value = data.map(item => ({
      value: item.item_code,
      label: item.item_value
    }))
  } catch (error) {
    console.error('获取业务名称选项失败:', error)
    businessOptions.value = []
    ElMessage.warning('获取业务名称选项失败')
  }
}

// 获取业务类型选项
const loadBusinessTypeOptions = async () => {
  try {
    const config = {
      params: {
        dict_code: 'eq.zxkh0002',
        deleted: `eq.0`
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/dictionary_cfg', {}, config)
    const data = response.data || []

    businessTypeOptions.value = data.map(item => ({
      value: item.item_code,
      label: item.item_value
    }))
  } catch (error) {
    console.error('获取业务类型选项失败:', error)
    businessTypeOptions.value = []
    ElMessage.warning('获取业务类型选项失败')
  }
}

// 获取折算标的选项
const loadCnvrSubjOptions = async () => {
  try {
    const config = {
      params: {
        dict_code: 'eq.zxkh0003',
        deleted: `eq.0`
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/dictionary_cfg', {}, config)
    const data = response.data || []

    cnvrSubjOptions.value = data.map(item => ({
      value: item.item_code,
      label: item.item_value
    }))
  } catch (error) {
    console.error('获取折算标的选项失败:', error)
    cnvrSubjOptions.value = []
    ElMessage.warning('获取折算标的选项失败')
  }
}

// 初始化数据
onMounted(() => {
  loadBusinessOptions()
  loadBusinessTypeOptions()
  loadCnvrSubjOptions()  // 新增加载折算标的选项
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }
    // 构建查询参数
    const filters = {}

    // 添加查询条件
    if (queryForm.businessNames && queryForm.businessNames.length > 0) {
      const businessNamesStr = queryForm.businessNames.map(name => `"${name}"`).join(',')
      filters.busi_name_cd = `in.(${businessNamesStr})`
    }

    // 添加last_flag=1条件
    filters.last_flag = 'eq.1'

    // 添加分页参数
    const offset = (currentPage.value - 1) * pageSize.value
    const limit = pageSize.value

    const config = {
      params: {
        ...filters,
        order: 'tect_strt_date.desc,crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Range': `${offset}-${offset + limit - 1}`,
        'Accept-Profile': 'mkt_base'
      }
    }

  http.get('/v_busi_equi_cnvr_coef_set', {}, config)
    .then(response => {
      tableData.value = response.data || []
      totalCount.value = response.total || 0
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 重置查询
const resetQuery = () => {
  queryForm.businessNames = []
  handleQuery()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增创新业务权益折算系数'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row) => {
  dialogTitle.value = '修改创新业务权益折算系数'
  isEdit.value = true
  resetForm()

  // 保存原始开始日期（用于限制后续不能往回选）
  originalStartDate.value = row.tect_strt_date;

  // 填充表单数据
  Object.assign(formData, row)

  dialogVisible.value = true
}

// 查看历史记录
const handleHistory = async (row) => {
  try {
    // 构建查询参数，获取该业务的所有历史记录
    const config = {
      params: {
        busi_name_cd: `eq.${row.busi_name_cd}`,
        busi_type: `eq.${row.busi_type}`,
        order: 'crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    // 获取历史记录数据（不添加last_flag条件）
    const response = await http.get('/v_busi_equi_cnvr_coef_set', {}, config)
    const data = response.data || []

    // 为每条记录添加业务名称显示
    historyData.value = data.map(item => {
      const businessOption = businessOptions.value.find(option => option.value === item.busi_name_cd)
      return {
        ...item,
        busi_name: businessOption ? businessOption.label : item.busi_name_cd
      }
    })
    historyDialogVisible.value = true

  } catch (error) {
    console.error('获取历史记录时发生错误:', error)
    ElMessage.error('获取历史记录失败，请检查网络连接')
  }
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const filters = {}

    // 添加查询条件
    if (queryForm.businessNames && queryForm.businessNames.length > 0) {
      const businessNamesStr = queryForm.businessNames.map(name => `"${name}"`).join(',')
      filters.busi_name_cd = `in.(${businessNamesStr})`
    }

    // 添加last_flag=1条件
    filters.last_flag = 'eq.1'

    const config = {
      params: {
        ...filters,
        order: 'tect_strt_date.desc,crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.get('/v_busi_equi_cnvr_coef_set', {}, config)
    const allData = response.data || []

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 为每条记录添加业务名称显示
    const exportData = allData.map(item => {
      const businessOption = businessOptions.value.find(option => option.value === item.busi_name_cd)
      const businessTypeOption = businessTypeOptions.value.find(option => option.value === item.busi_type)
      const cnvrSubjOption = cnvrSubjOptions.value.find(option => option.value === item.cnvr_subj)
      return {
        ...item,
        busi_name: businessOption ? businessOption.label : item.busi_name_cd,
        busi_type_name: businessTypeOption ? businessTypeOption.label : item.busi_type,
        cnvr_subj_name: cnvrSubjOption ? cnvrSubjOption.label : item.cnvr_subj
      }
    })

    // 准备导出数据
    const excelData = [
      // 表头
      ['生效开始日期', '生效结束日期', '业务名称', '业务类型', '折算标的', '权益折算系数', '创建时间', '更新时间'],
      // 数据行
      ...exportData.map(item => [
        item.tect_strt_date || '',
        item.tect_end_date || '',
        item.busi_name || '',
        item.busi_type_name || '',
        item.cnvr_subj_name || '',
        formatNumber(item.equi_cnvr_coef),
        formatDateTime(item.crt_time),
        formatDateTime(item.upd_time)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(excelData)

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 生效开始日期
      { wch: 15 }, // 生效结束日期
      { wch: 20 }, // 业务名称
      { wch: 15 }, // 业务类型
      { wch: 15 }, // 折算标的
      { wch: 15 }, // 权益折算系数
      { wch: 20 }, // 创建时间
      { wch: 20 }  // 更新时间
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '创新业务权益折算系数设置')

    // 生成文件名
    const now = new Date()
    const fileName = `创新业务权益折算系数设置_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)
  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const requestData = {
          i_request: {
            optionflg: isEdit.value ? "2" : "1",
            oacode: urlParams.oacde,
            tect_strt_date: formData.tect_strt_date,
            tect_end_date: formData.tect_end_date,
            busi_name: formData.busi_name_cd,
            busi_type: formData.busi_type,
            cnvr_subj: formData.cnvr_subj,
            equi_cnvr_coef: formData.equi_cnvr_coef
          }
        }

        // 如果是编辑，需要添加uuid
        if (isEdit.value && formData.uuid) {
          requestData.i_request.uuid = formData.uuid
        }

        const response = await http.post('/rpc/p_busi_equi_cnvr_coef_set_e', requestData, {
          headers: {
            'Content-Profile': 'mkt_base',
            'Content-Type': 'application/json'
          }
        })

        if (response.data && response.data.o_status === 0) {
          const result = response.data

          if (result.o_status === 0) {
            ElMessage.success(isEdit.value ? '修改成功' : '新增成功')
            dialogVisible.value = false
            handleQuery() // 重新查询数据
          } else {
            ElMessage.error(result.o_msg || '操作失败')
          }
        } else {
          ElMessage.error(response.data?.o_msg)
        }
      } catch (error) {
        console.error('提交数据时发生错误:', error)
        ElMessage.error('操作失败，请检查网络连接')
      }
    }
  })
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该条记录？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const requestData = {
      i_request: {
        optionflg: "3",
        oacode: urlParams.oacde,
        uuid: row.uuid
      }
    }

    const response = await http.post('/rpc/p_busi_equi_cnvr_coef_set_e', requestData, {
      headers: {
        'Content-Profile': 'mkt_base',
        'Content-Type': 'application/json'
      }
    })

    if (response.data && response.data.o_status === 0) {
      const result = response.data

      if (result.o_status === 0) {
        ElMessage.success('删除成功')
        handleQuery() // 重新查询数据
      } else {
        ElMessage.error(result.o_msg || '删除失败')
      }
    } else {
      ElMessage.error('删除失败，请检查网络连接')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除数据时发生错误:', error)
      ElMessage.error('删除失败，请检查网络连接')
    }
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }

  Object.assign(formData, {
    tect_strt_date: '',
    tect_end_date: '',
    busi_name_cd: '',
    busi_name: '',
    busi_type: '',
    cnvr_subj: '',
    equi_cnvr_coef: null,
    uuid: ''
  })
}

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}


// 原始的开始日期（编辑时保存）
const originalStartDate = ref(""); // 例如 '2024-03'

// 远程加载或初始化时设置原始值
function openEditDialog(row) {
  dialogTitle.value = "修改";
  isEdit.value = true;

  // 保存原始开始日期（用于限制后续不能往回选）
  originalStartDate.value = row.tect_strt_date; // 如 '2024-03'

  formData.value = { ...row };
  dialogVisible.value = true;
}

// 限制开始日期不能早于原始日期
const disabledStartDate = (date) => {
  if (!originalStartDate.value || !isEdit.value) return false;

  // 将原始日期字符串转为 Date 对象（月初）
  const originalDate = new Date(originalStartDate.value + "-01");
  // 当前选择的月份对应的日期（转为月初比较）
  const currentDate = new Date(
    date.getFullYear() +
      "-" +
      (date.getMonth() + 1).toString().padStart(2, "0") +
      "-01"
  );

  // 禁用所有早于原始开始日期的月份
  return currentDate < originalDate;
};
</script>

<style lang="scss" scoped>
/* empty */
</style>
