-- 营销管理系统数据库初始化脚本

-- 创建分支机构分类评级表
CREATE TABLE IF NOT EXISTS `branch_rating` (
    `id` varchar(64) NOT NULL COMMENT '主键ID',
    `effective_start_date` varchar(7) NOT NULL COMMENT '生效开始日期(YYYY-MM)',
    `effective_end_date` varchar(7) NOT NULL COMMENT '生效结束日期(YYYY-MM)',
    `branch_code` varchar(20) NOT NULL COMMENT '分支机构代码',
    `branch_name` varchar(100) NOT NULL COMMENT '分支机构名称',
    `rating_level` varchar(10) NOT NULL COMMENT '分类评定等级(A/B/C)',
    `is_latest` tinyint(1) DEFAULT 1 COMMENT '是否为最新记录(0-否 1-是)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标志(0-存在 1-删除)',
    PRIMARY KEY (`id`),
    KEY `idx_branch_code` (`branch_code`),
    KEY `idx_effective_date` (`effective_start_date`, `effective_end_date`),
    KEY `idx_create_time` (`create_time`),
    UNIQUE KEY `uk_branch_effective` (`branch_code`, `effective_start_date`, `deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分支机构分类评级表';

-- 插入示例数据
INSERT INTO `branch_rating` (`id`, `effective_start_date`, `effective_end_date`, `branch_code`, `branch_name`, `rating_level`, `is_latest`, `create_by`, `update_by`) VALUES
('1', '2024-01', '2024-12', '1001', '上海浦东营业部', 'A', 1, 'system', 'system'),
('2', '2024-01', '2024-12', '1002', '上海黄浦营业部', 'B', 1, 'system', 'system'),
('3', '2024-01', '2024-12', '1033', '北京海淀区营业部', 'A', 1, 'system', 'system'),
('4', '2024-01', '2024-12', '1034', '北京朝阳区营业部', 'B', 1, 'system', 'system'),
('5', '2024-01', '2024-12', '1045', '广州天河区营业部', 'C', 1, 'system', 'system');

-- 创建用户表
CREATE TABLE IF NOT EXISTS `sys_user` (
    `id` varchar(64) NOT NULL COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(100) NOT NULL COMMENT '密码',
    `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态(0-禁用 1-启用)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标志(0-存在 1-删除)',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`, `deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统用户表';

-- 插入默认管理员用户
INSERT INTO `sys_user` (`id`, `username`, `password`, `real_name`, `email`, `status`) VALUES
('1', 'admin', '$2a$10$7JB720yubVSOfvVMe6/YqO4wkhWGEn67XVb1TqHGjpQYqHSHhcpNi', '系统管理员', '<EMAIL>', 1);
