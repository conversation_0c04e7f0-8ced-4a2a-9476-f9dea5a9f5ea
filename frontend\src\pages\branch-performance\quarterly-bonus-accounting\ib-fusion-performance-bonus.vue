<template>
  <div class="report-title">
    <h2>IB融合绩效奖金核算表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计日期" required>
              <el-date-picker
                v-model="queryForm.dataDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery" :loading="loading">查询</el-button>
              <el-button type="warning" @click="handleExport" :loading="exportLoading">导出</el-button>
              <el-button
                type="info"
                @click="goBack"
                v-if="urlParams.goBack === 'true'"
              >
                返回
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="data_date_range" label="日期" fixed="left" width="150" />
        <el-table-column prop="brh_cd" label="分支机构代码" width="120" />
        <el-table-column prop="brh_nam" label="分支机构名称" width="150" />
        <el-table-column prop="workload_base" label="季度IB工作量奖金分配基数" width="180" align="right" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.workload_base) }}
          </template>
        </el-table-column>
        <el-table-column prop="result_base" label="季度IB业务成果奖金分配基数" width="180" align="right" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.result_base) }}
          </template>
        </el-table-column>
        <el-table-column prop="workload_score" label="当期IB工作量得分" width="140" align="right" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.workload_score) }}
          </template>
        </el-table-column>
        <el-table-column prop="result_score" label="当期IB业务成果得分" width="150" align="right" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.result_score) }}
          </template>
        </el-table-column>
        <el-table-column prop="workload_cover_rate" label="季度IB工作量覆盖率" width="150" align="right" sortable>
          <template #default="scope">
            {{ formatPercentage(scope.row.workload_cover_rate) }}
          </template>
        </el-table-column>
        <el-table-column prop="workload_bonus" label="IB工作量分配绩效奖金" width="170" align="right" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.workload_bonus) }}
          </template>
        </el-table-column>
        <el-table-column prop="result_bonus" label="IB业务成果分配绩效奖金" width="170" align="right" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.result_bonus) }}
          </template>
        </el-table-column>
        <el-table-column prop="total_bonus" label="IB融合绩效奖金金额" width="160" align="right" sortable>
          <template #default="scope">
            <span class="total-bonus">{{ formatNumber(scope.row.total_bonus) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

const router = useRouter()
const route = useRoute()

// 获取URL参数，参考 employee_info_statistics
const oacode = ref(route.query.oacode || 'system')
const srcsys = ref(route.query.srcsys || '1')

// 获取默认日期（上个月）
const getDefaultDate = () => {
  const now = new Date()
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  return `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`
}

// 查询表单
const queryForm = reactive({
  dataDate: getDefaultDate(),
  branchCode: ''
})

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)
const exportLoading = ref(false)

// 表格数据
const tableData = ref([])

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacode: params.get('oacode') || oacode.value,
    roleid: params.get('roleid') || '001',
    srcsys: params.get('srcsys') || srcsys.value,
    goBack: params.get('goback') || false,
  }
}
const urlParams = getUrlParams()

// 返回上一页
const goBack = () => {
  router.back()
}

// 格式化数字
const formatNumber = (num) => {
  if (num === null || num === undefined || num === '') return '0.00'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 格式化百分比
const formatPercentage = (num) => {
  if (num === null || num === undefined || num === '') return '0.00%'
  return (Number(num) * 100).toFixed(2) + '%'
}

// 初始化数据
onMounted(() => {
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询方法
const handleQuery = async () => {
  // 验证必填字段
  if (!queryForm.dataDate) {
    ElMessage.warning('请选择统计日期')
    return
  }

  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构建函数参数
  const functionParams = {
    oacode: urlParams.oacode,
    roleid: urlParams.roleid,
    srcsys: urlParams.srcsys,
    goBack: urlParams.goBack,
    p_data_date: queryForm.dataDate,
    p_brh_cd: queryForm.branchCode || null
  }

  const config = {
    headers: {
      'Accept': 'application/json',
      'Content-Profile': 'mkt_base'
    }
  }

  http.callFunction('p_ib_fusion_perf_bonus_s', functionParams, config)
  .then(response => {
    const rawData = response.data || []

    // 由于函数已经进行了汇总，直接使用返回的数据
    // 但需要进行分页处理
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    const paginatedData = rawData.slice(startIndex, endIndex)

    tableData.value = paginatedData
    totalCount.value = rawData.length
  })
  .catch(error => {
    console.error('API请求失败:', error)
    ElMessage.error('获取数据失败')
  })
  .finally(() => {
    lastSuccessfulForm = JSON.stringify(queryForm);
    loading.value = false
  })
}

// 导出方法
const handleExport = async () => {
  // 验证必填字段
  if (!queryForm.dataDate) {
    ElMessage.warning('请选择统计日期')
    return
  }

  // 防止重复点击
  if (exportLoading.value) {
    return
  }

  exportLoading.value = true

  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建函数参数
    const functionParams = {
      oacode: urlParams.oacode,
      srcsys: urlParams.srcsys,
      roleid: urlParams.roleid,
      goBack: urlParams.goBack,
      p_data_date: queryForm.dataDate,
      p_brh_cd: queryForm.branchCode || null
    }

    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.callFunction('p_ib_fusion_perf_bonus_s', functionParams, config)
    const allData = response.data || []

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '日期', '分支机构代码', '分支机构名称', '季度IB工作量奖金分配基数', '季度IB业务成果奖金分配基数',
        '当期IB工作量得分', '当期IB业务成果得分', '季度IB工作量覆盖率', 'IB工作量分配绩效奖金',
        'IB业务成果分配绩效奖金', 'IB融合绩效奖金金额'
      ],
      // 数据行
      ...allData.map(item => [
        item.data_date_range || '',
        item.brh_cd || '',
        item.brh_nam || '',
        formatNumber(item.workload_base),
        formatNumber(item.result_base),
        formatNumber(item.workload_score),
        formatNumber(item.result_score),
        formatPercentage(item.workload_cover_rate),
        formatNumber(item.workload_bonus),
        formatNumber(item.result_bonus),
        formatNumber(item.total_bonus)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 日期
      { wch: 15 }, // 分支机构代码
      { wch: 20 }, // 分支机构名称
      { wch: 20 }, // 季度IB工作量奖金分配基数
      { wch: 20 }, // 季度IB业务成果奖金分配基数
      { wch: 18 }, // 当期IB工作量得分
      { wch: 18 }, // 当期IB业务成果得分
      { wch: 18 }, // 季度IB工作量覆盖率
      { wch: 20 }, // IB工作量分配绩效奖金
      { wch: 20 }, // IB业务成果分配绩效奖金
      { wch: 18 }  // IB融合绩效奖金金额
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, 'IB融合绩效奖金核算表')

    // 生成文件名
    const now = new Date()
    const fileName = `IB融合绩效奖金核算表_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  } finally {
    exportLoading.value = false
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 处理排序变化
const handleSortChange = ({ column, prop, order }) => {
  // 这里可以根据需要实现排序逻辑
  // 由于是前端分页，可以直接对tableData进行排序
  if (prop && order) {
    const isAsc = order === 'ascending'
    tableData.value.sort((a, b) => {
      const aVal = Number(a[prop]) || 0
      const bVal = Number(b[prop]) || 0
      return isAsc ? aVal - bVal : bVal - aVal
    })
  }
}

</script>

<style lang="scss" scoped>
.report-title {
  padding: 20px;

  h2 {
    margin: 0 0 20px 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }
}

.query-card {
  margin-bottom: 20px;

  .query-form {
    .el-form-item {
      margin-bottom: 16px;
    }

    .button-group {
      text-align: left;
      margin-top: 10px;

      .el-button {
        margin-right: 10px;
      }
    }
  }
}

.table-card {
  .el-table {
    // 表头样式
    :deep(.el-table__header-wrapper) {
      .el-table__header {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 600;
          white-space: nowrap;

          .cell {
            padding: 8px 12px;
            text-align: center;
            vertical-align: middle;
          }
        }
      }
    }

    // 表体样式
    :deep(.el-table__body-wrapper) {
      .el-table__body {
        td {
          .cell {
            padding: 8px 12px;
            max-width: 400px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            &:hover {
              overflow: visible;
              white-space: normal;
              word-break: break-all;
            }
          }
        }
      }
    }

    // 固定列样式
    :deep(.el-table__fixed-left) {
      box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
    }
  }

  // 总奖金金额特殊样式
  .total-bonus {
    font-weight: 600;
    color: #e6a23c;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

// 响应式设计
@media (max-width: 1200px) {
  .el-table {
    font-size: 12px;

    :deep(.el-table__header-wrapper) {
      .el-table__header {
        th .cell {
          padding: 6px 8px;
        }
      }
    }

    :deep(.el-table__body-wrapper) {
      .el-table__body {
        td .cell {
          padding: 6px 8px;
        }
      }
    }
  }
}

// 加载状态样式
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
}

// 表格边框和条纹样式优化
:deep(.el-table--border) {
  border: 1px solid #ebeef5;

  &.el-table--striped {
    .el-table__body {
      tr.el-table__row--striped {
        td {
          background-color: #fafafa;
        }
      }
    }
  }
}
</style>
