<template>
  <div class="report-title">
    <h2>分支机构季度绩效奖金核算表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计日期" required>
              <el-date-picker
                v-model="queryForm.quarter"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="data_date" label="日期" fixed="left" />
        <el-table-column prop="brh_cd" label="分支机构代码" />
        <el-table-column prop="brh_nam" label="分支机构名称" />
        <el-table-column prop="year_p_tgt" label="年度利润目标" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.year_p_tgt) }}
          </template>
        </el-table-column>
        <el-table-column prop="curr_p_comp" label="当期利润完成值" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.curr_p_comp) }}
          </template>
        </el-table-column>
        <el-table-column prop="curr_perf_exam_scor" label="当期绩效考核得分" align="right" />
        <el-table-column prop="curr_tgt_p_comp" label="当期目标利润完成率" align="right" >
          <template #default="scope">
            {{ formatPercent(scope.row.curr_tgt_p_comp) }}
          </template>
        </el-table-column>
        <el-table-column prop="perf_bons_rati" label="绩效奖金计提比例" align="right" >
          <template #default="scope">
            {{ formatPercent(scope.row.perf_bons_rati) }}
          </template>
        </el-table-column>
        <el-table-column prop="qua_perf_bons_arrg" label="季度绩效奖金基数" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.qua_perf_bons_arrg) }}
          </template>
        </el-table-column>
        <el-table-column prop="qua_p_dod_naad" label="季度利润环比增长额" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.qua_p_dod_naad) }}
          </template>
        </el-table-column>
        <el-table-column prop="p_comp_rate_perf_bons" label="利润完成率绩效奖金" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.p_comp_rate_perf_bons) }}
          </template>
        </el-table-column>
        <el-table-column prop="qua_p_dod_perf_bons" label="季度利润环比增长绩效奖金" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.qua_p_dod_perf_bons) }}
          </template>
        </el-table-column>
        <el-table-column prop="qua_perf_bons_amt" label="季度绩效奖金金额" align="right" fixed="right">
          <template #default="scope">
            {{ formatNumber(scope.row.qua_perf_bons_amt) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

const router = useRouter()
const route = useRoute()

// 获取URL参数
const oacode = ref(route.query.oacode || 'system')
const srcsys = ref(route.query.srcsys || '1')

// 获取默认季度
const getDefaultQuarter = () => {
  const now = new Date()
  const currentMonth = now.getMonth()
  let quarter
  
  if (currentMonth < 3) quarter = 1
  else if (currentMonth < 6) quarter = 2
  else if (currentMonth < 9) quarter = 3
  else quarter = 4
  
  return `${now.getFullYear()}-0${quarter * 3 - 2}`
}

// 获取默认月份（当前月份）
const getDefaultMonth = () => {
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth() // 0-11，需要+1
  
  // 当前系统时间上一个月份
  let endYear = currentYear
  let endMonth = currentMonth // 当前月份的上一个月

  if (endMonth === 0) { // 如果当前是1月，上一个月是去年12月
    endYear = currentYear - 1
    endMonth = 12
  }

  return `${endYear}-${String(endMonth).padStart(2, '0')}`
}

// 查询表单
const queryForm = reactive({
  quarter: getDefaultMonth(),
  branchCode: ''
})

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacode: params.get('oacode') || oacode.value,
    roleid: params.get('roleid') || '001',
    srcsys: params.get('srcsys') || srcsys.value,
    goBack: params.get('goback') || false,
  }
}
const urlParams = getUrlParams()

// 格式化数字
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 格式化百分比
const formatPercent = (num) => {
  if (num === null || num === undefined) return '0%'
  return (Number(num) * 100).toFixed(2) + '%'
}

// 初始化数据
onMounted(() => {
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询方法
const handleQuery = async () => {
  // 验证必填字段
  if (!queryForm.quarter) {
    ElMessage.warning('请选择统计季度')
    return
  }

  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // handleQuery方法中的修改
  // 构建函数参数
  const functionParams = {
    oacode: urlParams.oacode,
    srcsys: urlParams.srcsys,
    roleid: urlParams.roleid,
    goBack: urlParams.goBack,
    p_data_date: queryForm.quarter,  // 修改：p_quarter -> p_month
    p_brh_cd: queryForm.branchCode || null
  }

  const config = {
    headers: {
      'Accept': 'application/json',
      'Content-Profile': 'mkt_base'  // 修改：mkt_base -> mkt_mang
    }
  }

  http.callFunction('p_brh_qua_perf_bons_s', functionParams, config)
  .then(response => {
    let rawData = response.data || []

    // 对数据进行排序
    rawData = rawData.sort((a, b) => {
      return a.brh_cd.localeCompare(b.brh_cd)
    })

    // 进行分页处理
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    const paginatedData = rawData.slice(startIndex, endIndex)

    tableData.value = paginatedData
    totalCount.value = rawData.length
  })
  .catch(error => {
    console.error('API请求失败:', error)
    ElMessage.error('获取数据失败')
  })
  .finally(() => {
    lastSuccessfulForm = JSON.stringify(queryForm);
    loading.value = false
  })
}

// 导出方法
const handleExport = async () => {
  // 验证必填字段
  if (!queryForm.quarter) {
    ElMessage.warning('请选择统计季度')
    return
  }

  try {
    ElMessage.info('正在导出数据，请稍候...')

    // handleExport方法中的修改
    // 构建函数参数
    const functionParams = {
      oacode: urlParams.oacode,
      srcsys: urlParams.srcsys,
      roleid: urlParams.roleid,
      goBack: urlParams.goBack,
      p_data_date: queryForm.quarter,  // 修改：p_quarter -> p_month
      p_brh_cd: queryForm.branchCode || null
    }

    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'  // 修改：mkt_base -> mkt_mang
      }
    }

    // 获取所有数据
    const response = await http.callFunction('p_brh_qua_perf_bons_s', functionParams, config)
    const aggregatedData = response.data || []

    if (aggregatedData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '日期', '分支机构代码', '分支机构名称', '年度利润目标', '当期利润完成值', 
        '当期绩效考核得分', '当期目标利润完成率', '绩效奖金计提比例', 
        '季度绩效奖金基数', '季度利润环比增长额', '利润完成率绩效奖金', 
        '季度利润环比增长绩效奖金', '季度绩效奖金金额'
      ],
      // 数据行
      ...aggregatedData.map(item => [
        item.data_date || '',
        item.brh_cd || '',
        item.brh_nam || '',
        formatNumber(item.year_p_tgt),
        formatNumber(item.curr_p_comp),
        item.curr_perf_exam_scor || '',
        formatPercent(item.curr_tgt_p_comp),
        formatPercent(item.perf_bons_rati),
        formatNumber(item.qua_perf_bons_arrg),
        formatNumber(item.qua_p_dod_naad),
        formatNumber(item.p_comp_rate_perf_bons),
        formatNumber(item.qua_p_dod_perf_bons),
        formatNumber(item.qua_perf_bons_amt)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 30 },  // 日期
      { wch: 15 },  // 分支机构代码
      { wch: 25 },  // 分支机构名称
      { wch: 15 },  // 年度利润目标
      { wch: 15 },  // 当期利润完成值
      { wch: 15 },  // 当期绩效考核得分
      { wch: 15 },  // 当期目标利润完成率
      { wch: 15 },  // 绩效奖金计提比例
      { wch: 15 },  // 季度绩效奖金基数
      { wch: 15 },  // 季度利润环比增长额
      { wch: 15 },  // 利润完成率绩效奖金
      { wch: 20 },  // 季度利润环比增长绩效奖金
      { wch: 15 }   // 季度绩效奖金金额
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '季度绩效奖金核算表')

    // 生成文件名
    const now = new Date()
    const fileName = `季度绩效奖金核算表_${queryForm.quarter}_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${aggregatedData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc';
    queryForm.order = `${prop}.${sortOrder}`;
  } else {
    queryForm.order = '';
  }
  handleQuery();
}
</script>

<style lang="scss" scoped>
.report-title {
  h2 {
    margin-bottom: 20px;
    color: #303133;
  }
}

.query-card {
  margin-bottom: 20px;
  
  .query-form {
    .el-form-item {
      margin-bottom: 15px;
    }
  }
  
  .button-group {
    text-align: right;
    margin-top: 10px;
  }
}

.table-card {
  .el-table {
    width: 100%;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>